import React, {
  useContext,
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from "react";
import Plus from "../../../assets/images/svg_icon/add-icon.svg";
import BrandRegistration from "./BrandRegistration";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import Spinner from "../../../helpers/UI/Spinner";
import Loader from "../../../helpers/UI/Loader";
import { Link, useLocation } from "react-router-dom";
import { IntlContext } from "../../../App";
import { ChevronDown, PlusCircle, Edit, Layout, Trash2, X } from "lucide-react";
import Facebook from "../../../assets/images/Analytics/facebook.svg";
import PinterestLogo from "../../../assets/images/Analytics/pinterest.svg";
import VimoLogo from "../../../assets/images/Analytics/vimeo.svg";
import LinkedInLogo from "../../../assets/images/Analytics/linkedIn.svg";
import TwitterLogo from "../../../assets/images/Analytics/X.svg";
import TiktokLogo from "../../../assets/images/Analytics/tiktok.svg";
import RedditLogo from "../../../assets/images/Analytics/reddit.svg";
import ThreadsLogo from "../../../assets/images/Analytics/thread.svg";
import InstagramLogo from "../../../assets/images/Analytics/instagram.svg";
import YoutubeLogo from "../../../assets/images/Analytics/youtube.svg";
import TumblrLogo from "../../../assets/images/Analytics/tumblr.svg";
import Telegram from "../../../assets/images/Analytics/telegram.svg";
import Mastodon from "../../../assets/images/Analytics/mastodon.svg";
import addBrand from "../../../assets/images/svg_icon/addBrand.svg";
import BrandDropdown from "./BrandDropdown";
import Upload from "../../../assets/images/svg_icon/UploadImage.svg";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import dummyProfile from "../../../assets/images/dummtprofile.png";
import { Dialog } from "@mui/material";
import deleteSvg from "../../../assets/images/svg_icon/deleteBrand.svg";
import addIcon from "../../../assets/images/Brand_Management/add.svg";
import editIcon from "../../../assets/images/Brand_Management/edit.svg";
import editWhiteIcon from "../../../assets/images/Brand_Management/edit_white.svg";
import platformIcon from "../../../assets/images/Brand_Management/platform.svg";
import platformWhiteIcon from "../../../assets/images/Brand_Management/platform_white.svg";
import Cancel from "../../../assets/images/Analytics/Cance_icon.svg";
import ConnectDialog from "./ConnectPopup";
import deletIcon from "../../../assets/images/Brand_Management/delete.svg";
import { setApiMessage } from "../../../helpers/context/toaster";
import { useBrand } from "../../../helpers/context/BrandContext";

const PaginatedBrandList = () => {
  const { selectedBrand, handleBrandSelect, brands, setBrands } = useBrand();
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [isBrand, setIsBrand] = useState(false);
  const [loading, setLoading] = useState(true);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const [logo, setLogo] = useState(null);
  const itemsPerPage = 9;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isLogoLoading, setIsLogoLoading] = useState(false);

  const platformConfig = {
    facebook: { color: "#0866FF", icon: Facebook },
    pinterest: { color: "#E60023", icon: PinterestLogo },
    vimeo: { color: "#1EB8EB", icon: VimoLogo },
    linkedin: { color: "#0A66C2", icon: LinkedInLogo },
    twitter: { color: "#100E0F", icon: TwitterLogo },
    tiktok: { color: "#000000", icon: TiktokLogo },
    reddit: { color: "#FC471E", icon: RedditLogo },
    thread: { color: "#000000", icon: ThreadsLogo },
    instagram: { color: "#C30FB2", icon: InstagramLogo },
    youtube: { color: "#FF0302", icon: YoutubeLogo },
    tumblr: { color: "#35465C", icon: TumblrLogo },
    x: { color: "#000000", icon: TwitterLogo },
    telegram: { color: "#0088cc", icon: Telegram },
    mastodon: { color: "#6364FF", icon: Mastodon },
  };

  // Centralized social links cache
  const [socialLinksCache, setSocialLinksCache] = useState(new Map());
  const [isLoadingSocialLinks, setIsLoadingSocialLinks] = useState(false);

  const [profile, setProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const subscriptionId = localStorage.getItem("subscriptionId");
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [isLoadingDelete, setIsLoadingDelete] = useState(false);
  const location = useLocation();

  // Track active view/button
  const [activeView, setActiveView] = useState(
    location.state?.activeView || "default"
  );

  // Form data state - moved to useRef to prevent re-renders on field inputs
  const formDataRef = useRef({
    brand_id: "",
    brandName: "",
    email: "",
    officialDomain: "",
    logo: null,
  });

  const fetchSocialProfile = useCallback(
    async (brandId) => {
      if (socialLinksCache.has(brandId)) {
        return socialLinksCache.get(brandId);
      }

      try {
        const response = await apiInstance.get(URL.SHARE_PROFILE, {
          headers: {
            Authorization: `Bearer ${token}`,
            brand: brandId,
          },
        });

        if (response.data?.status && response.data?.profile) {
          const profileData = {
            social_links: response.data.profile.social_links || [],
            profile: response.data.profile,
          };

          // Cache the result
          setSocialLinksCache(
            (prev) => new Map(prev.set(brandId, profileData))
          );
          return profileData;
        }
        return { social_links: [], profile: null };
      } catch (error) {
        console.error("Error fetching social profile:", error);
        return { social_links: [], profile: null };
      }
    },
    [token, socialLinksCache]
  );

  // Optimized batch fetching of social links for multiple brands
  const fetchAllSocialLinks = useCallback(
    async (brandsList) => {
      setIsLoadingSocialLinks(true);

      try {
        // Only fetch for brands not in cache
        const uncachedBrands = brandsList.filter(
          (brand) => !socialLinksCache.has(brand.id)
        );

        if (uncachedBrands.length === 0) {
          // All brands are cached, just return cached data
          const brandsWithLinks = brandsList.map((brand) => ({
            ...brand,
            social_links: socialLinksCache.get(brand.id)?.social_links || [],
          }));
          return brandsWithLinks;
        }

        // Batch fetch uncached brands with controlled concurrency
        const BATCH_SIZE = 3; // Limit concurrent requests
        const results = [];

        for (let i = 0; i < uncachedBrands.length; i += BATCH_SIZE) {
          const batch = uncachedBrands.slice(i, i + BATCH_SIZE);
          const batchPromises = batch.map(async (brand) => {
            const profileData = await fetchSocialProfile(brand.id);
            return {
              ...brand,
              social_links: profileData.social_links,
            };
          });

          const batchResults = await Promise.all(batchPromises);
          results.push(...batchResults);
        }

        // Combine cached and newly fetched data
        const allBrandsWithLinks = brandsList.map((brand) => {
          const cached = socialLinksCache.get(brand.id);
          if (cached) {
            return { ...brand, social_links: cached.social_links };
          }
          const fetched = results.find((r) => r.id === brand.id);
          return fetched || brand;
        });

        return allBrandsWithLinks;
      } finally {
        setIsLoadingSocialLinks(false);
      }
    },
    [socialLinksCache, fetchSocialProfile]
  );

  // Get current brand's social data from cache or fetch if needed
  const getCurrentBrandSocialData = useCallback(
    async (brandId) => {
      if (!brandId) return { social_links: [], profile: null };

      const cached = socialLinksCache.get(brandId);
      if (cached) {
        setProfile(cached.profile);
        return cached;
      }

      // Fetch if not cached
      const profileData = await fetchSocialProfile(brandId);
      setProfile(profileData.profile);
      return profileData;
    },
    [socialLinksCache, fetchSocialProfile]
  );

  // Optimized GetBrands function - single API call, batch social links
  const GetBrands = async () => {
    try {
      setLoading(true);
      const response = await apiInstance.get(URL.GET_BRANDS);
      const fetchedBrands = response.data.data || [];
      setBrands(fetchedBrands);

      // Batch fetch social links for all brands
      const brandsWithLinks = await fetchAllSocialLinks(fetchedBrands);

      // Update selected brand if it exists
      if (selectedBrand) {
        const updatedBrand = brandsWithLinks.find(
          (b) => b.id === selectedBrand.id
        );
        if (updatedBrand) {
          handleBrandSelect(updatedBrand);
        }
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching brands:", error);
      setLoading(false);
    }
  };

  // Only fetch brands on mount
  useEffect(() => {
    GetBrands();
  }, []);

  // Memoized brands with social links from cache
  const brandsWithSocialLinks = useMemo(() => {
    if (!brands.length) return [];

    return brands.map((brand) => ({
      ...brand,
      social_links: socialLinksCache.get(brand.id)?.social_links || [],
    }));
  }, [brands, socialLinksCache]);

  // Update current brand social data when selectedBrand changes
  useEffect(() => {
    if (selectedBrand?.id) {
      getCurrentBrandSocialData(selectedBrand.id);
    }
  }, [selectedBrand?.id, getCurrentBrandSocialData]);

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const deleteFormData = new FormData();
  const deleteBrand = async () => {
    try {
      deleteFormData.append("brand_id", selectedBrand.id);
      const response = await apiInstance.post(URL.DELETE_BRAND, deleteFormData);
    } catch (error) {
      console.error("Error deleting brand:", error);
    }
  };

  const handleConfirmDelete = async () => {
    if (!selectedBrand) return;

    try {
      setIsLoadingDelete(true);
      await deleteBrand(selectedBrand);

      // Clear cache for deleted brand
      setSocialLinksCache((prev) => {
        const newCache = new Map(prev);
        newCache.delete(selectedBrand.id);
        return newCache;
      });

      await GetBrands();
      handleBrandSelect(brands[0]);
      setIsLoadingDelete(false);
      handleBrandSelect(null);
      setOpenDeleteDialog(false);

      setTimeout(() => {
        setActiveView("default");
      }, 300);
    } catch (error) {
      console.error("Failed to delete brand:", error);
      setIsLoadingDelete(false);
    }
  };

  const editBrand = async () => {
    try {
      setIsLogoLoading(true);
      const formData = new FormData();

      const brandId = formDataRef.current.brand_id || selectedBrand.id;
      const brandName = formDataRef.current.brandName || selectedBrand.name;
      const brandEmail = formDataRef.current.email || selectedBrand.email;
      const brandDomain =
        formDataRef.current.officialDomain || selectedBrand.domain;

      formData.append("brand_id", brandId);
      formData.append("name", brandName);
      formData.append("email", brandEmail);
      formData.append("domain", brandDomain);

      if (selectedFile instanceof File) {
        formData.append("logo_file", selectedFile);
      }

      const response = await apiInstance.post(URL.EDIT_BRAND, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      const updatedBrandFromAPI = response.data?.data;

      if (updatedBrandFromAPI) {
        const cachedSocialData = socialLinksCache.get(brandId);
        const updatedSelectedBrand = {
          ...selectedBrand,
          name: brandName,
          email: brandEmail,
          domain: brandDomain,
          social_links:
            cachedSocialData?.social_links || selectedBrand.social_links || [],
        };

        handleBrandSelect(updatedSelectedBrand);

        if (updatedBrandFromAPI.logo) {
          const logoUrl = `${URL.BASE_IMAGE_URL}${updatedBrandFromAPI.logo}`;
          setLogo(logoUrl);
        }
      }

      await GetBrands();
      setIsBrand(false);
      setActiveView("default");
      setIsLogoLoading(false);
    } catch (error) {
      console.error("Error updating brand:", error);
      setIsLogoLoading(false);
    }
  };

  // Update form data when a brand is selected
  useEffect(() => {
    if (selectedBrand) {
      formDataRef.current = {
        brand_id: selectedBrand.id,
        brandName: selectedBrand.name || "",
        email: selectedBrand.email || "",
        officialDomain: selectedBrand.domain || "",
      };

      if (selectedBrand.logo) {
        const logoUrl = `${URL.BASE_IMAGE_URL}${selectedBrand.logo}`;
        setLogo(logoUrl);
        setSelectedFile(null);
      } else {
        setLogo(null);
        setSelectedFile(null);
      }
    } else {
      formDataRef.current = {
        brandName: "",
        email: "",
        officialDomain: "",
      };
      setLogo(null);
    }
  }, [selectedBrand]);

  const filteredBrands = (brands || []).filter((brand) =>
    brand.name?.toLowerCase().includes((searchTerm || "").toLowerCase())
  );

  const totalPages = Math.max(
    1,
    Math.ceil(filteredBrands.length / itemsPerPage)
  );
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredBrands.slice(indexOfFirstItem, indexOfLastItem);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    formDataRef.current = {
      ...formDataRef.current,
      [name]: value,
    };

    if (e.target.hasAttribute("data-controlled")) {
      setActiveView((prev) => prev);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogo(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const toggleBrandRegistration = () => {
    setIsBrand(!isBrand);
  };

  const handleSave = () => {
    editBrand();
  };

  const handleRemoveLogo = () => {
    setLogo(null);
    setSelectedFile(null);
  };

  const getButtonStyle = (buttonName) => {
    const baseClasses =
      "flex items-center justify-center rounded-lg h-12 w-full px-4 py-3 cursor-pointer transition-colors duration-200";

    if (buttonName === activeView) {
      return `${baseClasses} bg-[#563D39] text-white`;
    } else if (buttonName === "delete") {
      return `${baseClasses} bg-[#FF00001A] text-red-500`;
    } else {
      return `${baseClasses} bg-[#563D391A] hover:bg-[#563D3940]`;
    }
  };

  // Optimized Platform View Component
  const PlatformsView = () => {
    const [dialogOpen, setDialogOpen] = useState(false);
    const [selectedPlatform, setSelectedPlatform] = useState(null);
    const [localLoading, setLocalLoading] = useState(false);
    // Disconnect modal state
    const [disconnectModalOpen, setDisconnectModalOpen] = useState(false);
    const [platformToDisconnect, setPlatformToDisconnect] = useState(null);
    const [disconnecting, setDisconnecting] = useState(false);

    // Get social links from cache instead of making API call
    const socialLinksData = useMemo(() => {
      if (!selectedBrand?.id) return { social_links: [], profile: null };
      return (
        socialLinksCache.get(selectedBrand.id) || {
          social_links: [],
          profile: null,
        }
      );
    }, [selectedBrand?.id, socialLinksCache]);

    const handlePlatformClick = (platform, isActive) => {
      if (!isActive) {
        setSelectedPlatform(platform);
        setDialogOpen(true);
      }
    };

    const handleConnectConfirm = async () => {
      try {
        setLocalLoading(true);
        const BrandId = selectedBrand.id;
        const platformName = selectedPlatform?.toLowerCase().trim();
        const apiUrl = URL[`${selectedPlatform.toUpperCase()}`];
        let response;
        if (platformName === "mastodon") {
          // Use POST for Mastodon connect
          const formData = new FormData();
          formData.append("instance_url", "https://mastodon.social");
          response = await apiInstance.post(apiUrl, formData, {
            headers: { brand: BrandId },
          });
        } else {
          // Use GET for all other platforms
          response = await apiInstance.get(apiUrl, {
            headers: { brand: BrandId },
          });
        }
        if (response.data?.status) {
          if (response.data.url) {
            window.location.href = response.data.url;
          } else {
            setDialogOpen(false);
            // Invalidate cache and refetch for this brand
            setSocialLinksCache((prev) => {
              const newCache = new Map(prev);
              newCache.delete(BrandId);
              return newCache;
            });
            await fetchSocialProfile(BrandId);
          }
        } else {
          setApiMessage("error", response.data.message);
        }
      } catch (error) {
        console.error("Error connecting platform:", error);
      } finally {
        setLocalLoading(false);
      }
    };

    // Disconnect logic
    const handleAvatarClick = (platform) => {
      setPlatformToDisconnect(platform);
      setDisconnectModalOpen(true);
    };

    const handleDisconnect = async () => {
      if (!platformToDisconnect) return;
      setDisconnecting(true);
      try {
        const BrandId = selectedBrand.id;
        // Normalize platform name for URL key
        let normalized = platformToDisconnect.platform?.toLowerCase();
        if (normalized === "threads") normalized = "thread";
        const apiUrl = URL[`DISCONNECT_${normalized.toUpperCase()}`];
        await apiInstance.get(apiUrl, { headers: { brand: BrandId } });
        // Invalidate cache and refetch for this brand
        setSocialLinksCache((prev) => {
          const newCache = new Map(prev);
          newCache.delete(BrandId);
          return newCache;
        });
        await fetchSocialProfile(BrandId);
        setDisconnectModalOpen(false);
        setPlatformToDisconnect(null);
      } catch (error) {
        setApiMessage("error", "Failed to disconnect platform");
      } finally {
        setDisconnecting(false);
      }
    };

    return (
      <>
        <div className="max-w-[1920px] flex flex-col justify-start items-center mx-auto bg-white border border-[#E0E0E0] pb-6 min-h-screen mt-8 rounded-[8px] px-4 sm:px-6 md:px-8">
          {isLoadingSocialLinks || localLoading ? (
            <div className="flex justify-center items-center w-full h-[75vh]">
              <Spinner />
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-2 gap-6 w-[80%] mt-10">
              {socialLinksData.social_links.length > 0 ? (
                socialLinksData.social_links.map((link) => {
                  const config = platformConfig[link.platform?.toLowerCase()];
                  const isActive = link.user_status;
                  const isPlatformStatus = link.platform_status;

                  if (isPlatformStatus === "2" || !config) return null;

                  return (
                    <div
                      key={link.platform}
                      className="relative flex items-center h-[69px] w-full bg-white border border-[#E0E0E0] rounded-[12px] "
                    >
                      <div
                        className="flex items-center justify-center h-full w-[45px] relative rounded-tl-[12px] rounded-bl-[12px] "
                        style={{ backgroundColor: config.color }}
                      >
                        <div className="bg-white p-2 rounded-full absolute top-[12px] -right-[20px] shadow-lg">
                          <img
                            src={config.icon}
                            alt={link.platform}
                            className="w-7 h-7"
                          />
                        </div>
                      </div>

                      <div className="flex-1 ml-4 sm:ml-6 text-sm font-medium text-gray-800 truncate">
                        {isActive
                          ? link.username || "Unnamed"
                          : "Not Connected"}
                      </div>

                      <div className="mr-4 shrink-0 w-8 h-8 r">
                        {isActive ? (
                          <div
                            className="relative cursor-pointer"
                            onClick={() => handleAvatarClick(link)}
                          >
                            <div className=" w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center">
                              <img
                                src={link?.profile_image || dummyProfile}
                                alt="Brand"
                                className="w-[32px] h-[32px] object-cover rounded-full "
                              />
                            </div>
                          </div>
                        ) : (
                          <button
                            onClick={() =>
                              handlePlatformClick(link.platform, isActive)
                            }
                            className="w-full h-full flex items-center justify-center "
                          >
                            <img
                              src={addBrand}
                              alt="+"
                              className="w-full h-full object-contain"
                            />
                          </button>
                        )}
                      </div>
                      {isActive && (
                        <div
                          className="h-4 w-6 absolute -top-[5px] -right-[12px] z-40 cursor-pointer"
                          onClick={() => handleAvatarClick(link)}
                        >
                          <img
                            src={Cancel}
                            alt=""
                            className=" rounded-full bg-Red h-4 w-4 p-1  "
                          />
                        </div>
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="col-span-2 text-center py-10 text-gray-500">
                  No platforms available
                </div>
              )}
            </div>
          )}
        </div>
        <ConnectDialog
          open={dialogOpen}
          onClose={() => setDialogOpen(false)}
          platform={selectedPlatform}
          onConfirm={handleConnectConfirm}
        />
        {/* Disconnect Confirmation Modal */}
        <Dialog
          open={disconnectModalOpen}
          onClose={() => setDisconnectModalOpen(false)}
        >
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center px-4 font-Ubuntu "
            onClick={() => setDisconnectModalOpen(false)}
          >
            <div
              className="bg-white rounded-2xl shadow-xl w-full max-w-sm p-6 relative"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Close Button */}
              <button
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
                onClick={() => setDisconnectModalOpen(false)}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M18 6L6 18M6 6L18 18"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>

              {/* Modal Content */}
              <div className="flex flex-col items-start justify-center text-center">
                {/* Platform Icon */}
                <div className="w-[45px] h-[45px] rounded-full flex items-center justify-center mb-4">
                  {platformToDisconnect &&
                    platformConfig[platformToDisconnect.platform?.toLowerCase()]
                      ?.icon && (
                      <img
                        src={
                          platformConfig[
                            platformToDisconnect.platform?.toLowerCase()
                          ].icon
                        }
                        alt={platformToDisconnect.platform}
                        className="w-[45px] h-[45px]"
                      />
                    )}
                </div>

                {/* Title */}
                <h2 className="text-[18px] font-bold text-[#000000] mb-1 capitalize">
                  Disconnect {platformToDisconnect?.platform}
                </h2>

                {/* Platform Name */}
                <div
                  className="text-[18px] font-bold mb-1 capitalize mt-[15px]"
                  style={{
                    color:
                      platformToDisconnect &&
                      platformConfig[
                        platformToDisconnect.platform?.toLowerCase()
                      ]?.color,
                  }}
                >
                  {platformToDisconnect?.platform}
                </div>

                {/* Message */}
                <p className="text-[#46484a] text-[14px] font-normal leading-[20px] max-w-xs text-left">
                  Are you sure you want to disconnect this platfrom from your
                  account?
                </p>
              </div>

              {/* Buttons */}
              <div className="flex justify-end gap-2 mt-6">
                <button
                  onClick={() => setDisconnectModalOpen(false)}
                  className="px-5 py-2 text-sm border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-100"
                  disabled={disconnecting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleDisconnect}
                  className="px-5 py-2 text-sm bg-[#4B2C27] text-white rounded-lg hover:bg-[#3c221f]"
                  disabled={disconnecting}
                >
                  {disconnecting ? "Disconnecting..." : "Disconnect"}
                </button>
              </div>
            </div>
          </div>
        </Dialog>
      </>
    );
  };

  // BrandForm component that can be used for both Add and Edit views
  const BrandForm = ({ title, buttonText, onSubmit, isDisabled = false }) => (
    <div className="max-w-[1920px] mx-auto bg-white border-[1px] border-[#E0E0E0] pb-6 min-h-screen mt-8 mb-10 lg:mb-0 rounded-[8px]">
      <div className="bg-[#F5F2F1] p-6 rounded-tr-md rounded-tl-md mb-8">
        <h1 className="text-[28px] font-bold text-[#563D39] mb-2">{title}</h1>
        <p className="text-base font-normal pr-[100px] xl:pr-[200px] text-[#5B6871] leading-[24px]">
          {localesData?.USER_WEB?.BRANDS?.BRANDFORM_TEXT}
        </p>
      </div>

      <div className="flex flex-col items-start gap-1 mb-8">
        <div className="w-[135px] h-[135px] border-2 border-dashed ms-[40px] bg-[#F8F8F8] border-gray-400 flex items-center justify-center rounded-[16px] mb-2 relative">
          {logo ? (
            <div className="relative w-full h-full group">
              <img
                src={logo}
                alt="Brand Logo"
                className="w-full h-full object-cover rounded-[16px]"
              />
              {!isDisabled && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-30 transition-all">
                  <div className="hidden group-hover:flex flex-col gap-2">
                    <label
                      htmlFor="brand-logo-edit"
                      className="bg-white text-[#563D39] px-2 py-1 rounded cursor-pointer text-xs hover:bg-[#F5F2F1]"
                    >
                      {localesData?.USER_WEB?.BRANDS?.CHANGE}
                    </label>
                    <button
                      onClick={handleRemoveLogo}
                      className="bg-red-50 text-red-500 px-2 py-1 rounded text-xs hover:bg-red-100"
                    >
                      {localesData?.USER_WEB?.BRANDS?.REMOVE}
                    </button>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center text-center gap-2">
              <img src={Upload} alt="Upload Logo" />
            </div>
          )}

          {/* Always have the input, but hide it */}
          {!isDisabled && (
            <input
              type="file"
              className="absolute inset-0 opacity-0 cursor-pointer w-full h-full"
              id="brand-logo-edit"
              onChange={handleFileChange}
              disabled={isDisabled}
              accept="image/*"
            />
          )}
        </div>

        <span className="text-xs text-gray-600 ms-[40px]">
          {logo
            ? selectedFile
              ? "Current logo"
              : "Current logo"
            : localesData?.USER_WEB?.BRANDS?.UPLOAD_YOUR_LOGO ||
              "Upload your logo"}
        </span>

        {!isDisabled && logo && (
          <div className="flex items-center gap-2 ms-[40px] mt-1">
            <button
              onClick={handleRemoveLogo}
              className="text-xs text-red-500 cursor-pointer underline"
            >
              {localesData?.USER_WEB?.BRANDS?.REMOVE}
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 max-w-4xl gap-4 mb-4 mx-[40px]">
        <div className="relative">
          <fieldset className="border border-gray-300 rounded-md px-3 pt-2 pb-3">
            <legend className="text-sm px-1 text-gray-700">
              <span className="px-1">
                {localesData?.USER_WEB?.BRANDS?.BRAND_NAME}
              </span>
            </legend>
            <input
              type="text"
              name="brandName"
              defaultValue={formDataRef.current.brandName}
              onChange={handleInputChange}
              className={`w-full border-none p-0 focus:ring-0 ${
                isDisabled ? " cursor-not-allowed" : ""
              }`}
              disabled={isDisabled}
            />
          </fieldset>
        </div>
        <div className="relative">
          <fieldset className="border border-gray-300 rounded-md px-3 pt-2 pb-3">
            <legend className="text-sm px-1 text-gray-700">
              <span className="px-1">
                {localesData?.USER_WEB?.BRANDS?.EMAIL}
              </span>
            </legend>
            <input
              type="email"
              name="email"
              defaultValue={formDataRef.current.email}
              onChange={handleInputChange}
              className={`w-full border-none p-0 focus:ring-0 ${
                isDisabled ? " cursor-not-allowed" : ""
              }`}
              disabled={isDisabled}
            />
          </fieldset>
        </div>
      </div>

      <div className="mb-6 max-w-[440px] mx-[40px]">
        <fieldset className="border border-gray-300 rounded-md px-3 pt-2 pb-3">
          <legend className="text-sm px-1 text-gray-700">
            <span className="px-1">
              {localesData?.USER_WEB?.BRANDS?.OFFICIAL_DOMAIN}
            </span>
          </legend>
          <input
            type="text"
            name="officialDomain"
            defaultValue={formDataRef.current.officialDomain}
            onChange={handleInputChange}
            className={`w-full border-none p-0 focus:ring-0 ${
              isDisabled ? " cursor-not-allowed" : ""
            }`}
            disabled={isDisabled}
          />
        </fieldset>
      </div>

      {buttonText && !isDisabled && (
        <div className="flex justify-center items-center mt-[70px]">
          <button
            type="button"
            onClick={onSubmit}
            className="bg-[#563D39] md:w-[280px] h-[40px] hover:bg-[#3f2f2b] text-white py-2 px-4 rounded-[10px] font-medium transition flex justify-center items-center"
            disabled={isLogoLoading}
          >
            {isLogoLoading ? <Spinner size="10" /> : buttonText}
          </button>
        </div>
      )}
    </div>
  );

  // Default view (empty state or brand info with disabled fields)
  const DefaultView = () => {
    if (!selectedBrand) {
      // Empty state when no brand is selected
      return (
        <div className="max-w-[1920px] mx-auto bg-white border-[1px] border-[#E0E0E0] pb-6 min-h-[73vh] mt-8 rounded-[8px]">
          <div className="flex flex-col items-center justify-center h-[50vh]">
            <img src={addBrand} alt="Add Brand" className="w-24 h-24 mb-4" />
            <h2 className="text-2xl font-semibold text-[#563D39] mb-3">
              {localesData?.USER_WEB?.BRANDS?.NO_BRAND_SELECTED}
            </h2>
            <p className="text-gray-600 mb-6 text-center max-w-md">
              {localesData?.USER_WEB?.BRANDS?.DEFAULTVIEW_TEXT}
            </p>
          </div>
        </div>
      );
    } else {
      // Brand view with disabled fields when a brand is selected
      return (
        <BrandForm title={`Brand: ${selectedBrand.name}`} isDisabled={true} />
      );
    }
  };

  return (
    <>
      <div className="mx-[30px] p-6 bg-white rounded-lg shadow-lg min-h-screen overflow-y-auto font-Ubuntu mt-[20px]">
        <div className="w-full px-2 sm:px-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6 mt-[10px]">
            {/* Dropdown Button */}
            <div className="relative w-full col-span-1 sm:col-span-2 xl:col-span-1">
              <button
                className="flex items-center justify-start gap-2 bg-[#563D391A] border rounded-lg h-12 w-full px-4 py-2 cursor-pointer"
                onClick={toggleDropdown}
              >
                {/* Brand Logo */}
                {selectedBrand?.logo && (
                  <img
                    src={`${URL.BASE_IMAGE_URL}${selectedBrand.logo}`}
                    alt="Brand Logo"
                    className="w-6 h-6 object-contain rounded-full"
                  />
                )}

                {/* Brand Name and Social Icons */}
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <span className="truncate text-[#000000] font-medium text-[18px]">
                    {selectedBrand ? selectedBrand.name : "Select Brand"}
                  </span>

                  {/* Connected Social Icons */}
                  {selectedBrand?.social_links && (
                    <div className="flex items-center -space-x-2 ml-2">
                      {selectedBrand.social_links
                        .filter((link) => link.user_status)
                        .slice(0, 3)
                        .map((link, index) => {
                          const config = platformConfig[link.platform];
                          return config ? (
                            <img
                              key={index}
                              src={config.icon}
                              alt={link.platform}
                              className="w-6 h-6 object-contain rounded-full p-0.5 "
                              style={{ zIndex: 10 + index }}
                            />
                          ) : null;
                        })}
                      {selectedBrand.social_links.filter(
                        (link) => link.user_status
                      ).length > 3 && (
                        <span className="text-xs text-gray-500 ml-1">
                          +
                          {selectedBrand.social_links.filter(
                            (link) => link.user_status
                          ).length - 3}
                        </span>
                      )}
                    </div>
                  )}
                </div>

                <ChevronDown
                  size={20}
                  className={`ml-auto transition-transform ${
                    isDropdownOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {isDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-full rounded-lg z-10">
                  <BrandDropdown
                    currentItems={brandsWithSocialLinks.slice(
                      indexOfFirstItem,
                      indexOfLastItem
                    )}
                    selectedBrand={selectedBrand}
                    onSelect={(brand) => {
                      handleBrandSelect(brand); // Use context method instead of setSelectedBrand
                      setIsDropdownOpen(false);
                      setActiveView("default");
                    }}
                    platformConfig={platformConfig}
                  />
                </div>
              )}
            </div>

            {/* Add Brand Button */}
            <button
              className={getButtonStyle(activeView === "add" ? "add" : "")}
              onClick={() => {
                setActiveView("add");
                toggleBrandRegistration();
              }}
            >
              <img
                src={addIcon}
                alt="Add Brand"
                className="h-[16px] w-[16px] mr-2 flex-shrink-0"
              />

              <span
                className={`truncate font-normal text-[14px] ${
                  activeView === "add" ? "text-[#FFFFFF]" : "text-[#A9ABAD]"
                }`}
              >
                {localesData?.USER_WEB?.BRANDS?.ADD}{" "}
              </span>
            </button>

            {/* Edit Brand Button */}
            <button
              className={getButtonStyle(activeView === "edit" ? "edit" : "")}
              onClick={() => {
                if (selectedBrand) {
                  // Pre-fill form with selected brand data when editing
                  formDataRef.current = {
                    brand_id: selectedBrand.id,
                    brandName: selectedBrand.name || "",
                    email: selectedBrand.email || "",
                    officialDomain: selectedBrand.domain || "",
                  };
                  setActiveView("edit");
                } else {
                  // Show alert or notification that no brand is selected
                  alert("Please select a brand to edit");
                }
              }}
              disabled={!selectedBrand}
            >
              <img
                src={activeView === "edit" ? editWhiteIcon : editIcon}
                alt="Edit Brand"
                className="h-[24px] w-[24px] mr-2 flex-shrink-0"
              />

              <span
                className={`truncate  font-normal text-[14px]  ${
                  activeView === "edit" ? "text-[#FFFFFF]" : "text-[#A9ABAD]"
                }`}
              >
                Edit Brand
              </span>
            </button>

            {/* Platforms Button */}
            <button
              className={getButtonStyle(
                activeView === "platforms" ? "platforms" : ""
              )}
              onClick={() => setActiveView("platforms")}
              disabled={!selectedBrand}
            >
              <img
                src={
                  activeView === "platforms" ? platformWhiteIcon : platformIcon
                }
                alt="Edit Brand"
                className="h-[24px] w-[24px] mr-2 flex-shrink-0"
              />
              <span
                className={`truncate font-normal text-[14px]  ${
                  activeView === "platforms"
                    ? "text-[#FFFFFF]"
                    : "text-[#A9ABAD]"
                }`}
              >
                {localesData?.USER_WEB?.BRANDS?.PLATFORMS}
              </span>
            </button>

            {/* Delete Brand Dialog */}
            <Dialog
              open={openDeleteDialog}
              onClose={() => setOpenDeleteDialog(false)}
            >
              <div
                className="fixed inset-0 bg-[#00000033] bg-opacity-50 flex items-center justify-center px-2 font-Ubuntu"
                onClick={() => setOpenDeleteDialog(false)}
              >
                <div
                  className="bg-white rounded-xl shadow-lg w-full max-w-lg p-10 relative"
                  onClick={(e) => e.stopPropagation()}
                >
                  <img
                    src={deleteSvg}
                    alt="Delete Brand"
                    className="w-[50px] h-[68px] mx-auto"
                  />
                  <h1 className="text-[14px] font-normal text-center text-Red mb-3 mt-3">
                    {localesData?.USER_WEB?.BRANDS?.DELETE_BRAND}
                  </h1>
                  <p className="font-normal text-[14px] text-[#535862] text-center">
                    {localesData?.USER_WEB?.BRANDS?.DELETE_BRAND_TEXT}
                  </p>

                  <div className="flex justify-center items-center gap-4 mt-5">
                    <button
                      onClick={() => setOpenDeleteDialog(false)}
                      className="px-6 py-2 border-[1px] border-[#E0E0E0] text-gray-800 h-[44px] w-[170px] rounded-[8px]"
                    >
                      {localesData?.USER_WEB?.BRANDS?.CANCEL}
                    </button>
                    <button
                      onClick={handleConfirmDelete}
                      className="px-6 py-2 bg-[#563D39] text-white h-[44px] w-[170px] rounded-[8px]"
                    >
                      {isLoadingDelete ? "Deleting..." : "Delete"}
                    </button>
                  </div>
                </div>
              </div>
            </Dialog>

            {/* Delete Brand Button */}
            <button
              className={getButtonStyle("delete")}
              onClick={() => {
                if (selectedBrand) {
                  setOpenDeleteDialog(true);
                } else {
                  alert("Please select a brand to delete");
                }
              }}
              disabled={!selectedBrand}
            >
              <img
                src={deletIcon}
                alt="Delete Brand"
                className="h-[20px] w-[20px] mr-2 flex-shrink-0"
              />
              <span className="truncate">
                {localesData?.USER_WEB?.BRANDS?.DELETE_BRAND}
              </span>
            </button>
          </div>
        </div>

        {/* Render view based on activeView state */}
        {loading ? (
          <div className="flex justify-center items-center h-[70vh]">
            <Spinner />
          </div>
        ) : (
          <>
            {activeView === "default" && <DefaultView />}

            {activeView === "add" && (
              <BrandRegistration
                GetBrands={GetBrands}
                setActiveView={setActiveView}
              />
            )}

            {activeView === "edit" && selectedBrand && (
              <BrandForm
                title={`Edit ${selectedBrand.name}`}
                buttonText="Save Changes"
                onSubmit={handleSave}
                isDisabled={false}
              />
            )}

            {activeView === "platforms" && <PlatformsView />}
          </>
        )}
      </div>
    </>
  );
};

export default PaginatedBrandList;
