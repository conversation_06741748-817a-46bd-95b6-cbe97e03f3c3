import React, { useState, useEffect, useCallback, useRef } from "react";
import { format } from "date-fns";

import Facebook from "../../../assets/images/Analytics/facebook.svg";
import PinterestLogo from "../../../assets/images/Analytics/pinterest.svg";
import Vimo<PERSON>ogo from "../../../assets/images/Analytics/vimeo.svg";
import LinkedInLogo from "../../../assets/images/Analytics/linkedIn.svg";
import TwitterLogo from "../../../assets/images/Analytics/X.svg";
import TiktokLogo from "../../../assets/images/Analytics/tiktok.svg";
import RedditLogo from "../../../assets/images/Analytics/reddit.svg";
import ThreadsLogo from "../../../assets/images/Analytics/thread.svg";
import InstagramLogo from "../../../assets/images/Analytics/instagram.svg";
import YoutubeLogo from "../../../assets/images/Analytics/youtube.svg";
import TumblrLogo from "../../../assets/images/Analytics/tumblr.svg";
import Telegram from "../../../assets/images/Analytics/telegram.svg";
import Mastodon from "../../../assets/images/Analytics/mastodon.svg";
import addBrand from "../../../assets/images/svg_icon/addBrand.svg";
import dummyProfile from "../../../assets/images/svg_icon/dummy_profile.svg";

import { fetchFromStorage } from "../../../helpers/context/storage.jsx";
import apiInstance from "../../../helpers/Axios/axiosINstance.jsx";
import { URL } from "../../../helpers/constant/Url.js";
import siteConstant from "../../../helpers/constant/siteConstant.js";
import Loader from "../../../helpers/UI/Loader.jsx";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";

const Sidebar = ({
  activeChannel,
  setActiveChannel,
  onPlatformSelect,
  dateRange,
  onPlatformChange,
  onPlatformDataChange,
  onMount,
  onChannelsLoaded,
}) => {
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const subscriptionId = localStorage.getItem("subscriptionId");

  const [socialLinks, setSocialLinks] = useState([]);
  const [selectedPlatform, setSelectedPlatform] = useState(
    activeChannel || null
  );
  const [isLoading, setIsLoading] = useState(true);
  const { selectedBrand } = useBrand();
  const BrandId = selectedBrand?.id;

  // Use refs to track current requests and prevent race conditions
  const currentBrandRef = useRef(null);
  const currentPlatformRequestRef = useRef(null);
  const isInitializedRef = useRef(false);

  // Call onMount only once
  useEffect(() => {
    if (onMount) onMount();
  }, [onMount]);

  const platformDateFormatMap = {
    facebook: "yyyy-MM-dd",
    instagram: "yyyy-MM-dd",
    linkedin: "dd-MM-yyyy",
    youtube: "yyyy-MM-dd",
    threads: "yyyy-MM-dd",
    pinterest: "yyyy-MM-dd",
    default: "yyyy-MM-dd",
  };

  const platformConfig = {
    facebook: { color: "#1877F2", icon: Facebook },
    pinterest: { color: "#E60023", icon: PinterestLogo },
    vimeo: { color: "#1AB7EA", icon: VimoLogo },
    linkedin: { color: "#0A66C2", icon: LinkedInLogo },
    twitter: { color: "#000000", icon: TwitterLogo },
    tiktok: { color: "#000000", icon: TiktokLogo },
    reddit: { color: "#FF4500", icon: RedditLogo },
    thread: { color: "#000000", icon: ThreadsLogo },
    instagram: { color: "#E1306C", icon: InstagramLogo },
    youtube: { color: "#FF0000", icon: YoutubeLogo },
    tumblr: { color: "#35465C", icon: TumblrLogo },
    x: { color: "#000000", icon: TwitterLogo },
    telegram: { color: "#0088cc", icon: Telegram },
    mastodon: { color: "#6364FF", icon: Mastodon },
    
  };

  // Create platforms from social links
  const platforms = socialLinks.map((link) => {
    const platformId = link.platform.toLowerCase();
    const platformData = platformConfig[platformId] || {
      color: "#808080",
      icon: null,
    };

    return {
      id: platformId,
      name: link.platform.charAt(0).toUpperCase() + link.platform.slice(1),
      color: platformData.color,
      icon: platformData.icon,
      hasLogo: !!platformData.icon,
      profile: link.profile_image || null,
      active: link.user_status || false,
      platformStatus: link.platform_status,
      platformData: link,
    };
  });

  // Function to get channels - not memoized to avoid circular deps
  const getChannels = async () => {
    if (!BrandId) return;

    const requestId = Date.now();
    currentBrandRef.current = requestId;

    setIsLoading(true);

    try {
      const response = await apiInstance.get(URL.SHARE_PROFILE, {
        headers: {
          Authorization: `Bearer ${token}`,
          brand: BrandId,
        },
      });

      // Check if this request is still current
      if (currentBrandRef.current !== requestId) {
        return; // Ignore outdated responses
      }

      if (response.data && response.data.status && response.data.profile) {
        setSocialLinks(response.data.profile.social_links || []);

        if (onChannelsLoaded) {
          onChannelsLoaded();
        }
      }
    } catch (error) {
      console.error("Error fetching channels:", error);
      if (currentBrandRef.current === requestId) {
        setSocialLinks([]);
      }
    } finally {
      if (currentBrandRef.current === requestId) {
        setIsLoading(false);
      }
    }
  };

  // Function to fetch platform data - not memoized to avoid circular deps
  const fetchPlatformData = async (platformId) => {
    if (!platformId || !BrandId || !dateRange) return;

    const currentPlatform = platforms.find((p) => p.id === platformId);
    if (!currentPlatform?.active) {
      if (onPlatformDataChange) {
        onPlatformDataChange(null);
      }
      return;
    }

    // Cancel previous request
    if (currentPlatformRequestRef.current) {
      currentPlatformRequestRef.current.cancelled = true;
    }

    const requestRef = { cancelled: false };
    currentPlatformRequestRef.current = requestRef;

    try {
      const dateFormat =
        platformDateFormatMap[platformId?.toLowerCase()] ||
        platformDateFormatMap.default;

      const commonPayload = {
        start_date: format(new Date(dateRange.startDate), dateFormat),
        end_date: format(new Date(dateRange.endDate), dateFormat),
      };

      const commonHeaders = {
        headers: {
          Authorization: `Bearer ${token}`,
          brand: BrandId,
          subscription: subscriptionId,
        },
      };

      let g1 = null,
        g2 = null,
        g3 = null,
        g4 = null,
        g5 = null;
      const platform = platformId?.toLowerCase();

      if (platform === "facebook") {
        const [resG1, resG2] = await Promise.all([
          apiInstance.post(
            URL.ANALYTICS_FACEBOOK_GARPH_1,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_FACEBOOK_GARPH_2,
            commonPayload,
            commonHeaders
          ),
        ]);
        if (!requestRef.cancelled) {
          g1 = resG1.data.data.graph_data;
          g2 = resG2.data.data.graph_data;
        }
      } else if (platform === "linkedin") {
        const [resG1, resG2, resG3] = await Promise.all([
          apiInstance.post(
            URL.ANALYTICS_LINKEDIN_GARPH_1,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_LINKEDIN_GARPH_2,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_LINKEDIN_GARPH_3,
            commonPayload,
            commonHeaders
          ),
        ]);
        if (!requestRef.cancelled) {
          g1 = resG1.data;
          g2 = resG2.data;
          g3 = resG3.data;
        }
      } else if (platform === "instagram") {
        const [resG1, resG2] = await Promise.all([
          apiInstance.post(
            URL.ANALYTICS_INSTAGRAM_GARPH_1,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_INSTAGRAM_GARPH_2,
            commonPayload,
            commonHeaders
          ),
        ]);
        if (!requestRef.cancelled) {
          g1 = resG1.data;
          g2 = resG2.data;
        }
      } else if (platform === "youtube") {
        const [resG1, resG2] = await Promise.all([
          apiInstance.post(
            URL.ANALYTICS_YOUTUBE_GARPH_1,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_YOUTUBE_GARPH_2,
            commonPayload,
            commonHeaders
          ),
        ]);
        if (!requestRef.cancelled) {
          g1 = resG1.data;
          g2 = resG2.data;
        }
      } else if (platform === "thread") {
        const [resG1, resG2, resG3, resG4, resG5] = await Promise.all([
          apiInstance.post(
            URL.ANALYTICS_THREADS_GARPH_1,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_THREADS_GARPH_2,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_THREADS_GARPH_3,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_THREADS_GARPH_4,
            commonPayload,
            commonHeaders
          ),
          apiInstance.post(
            URL.ANALYTICS_THREADS_GARPH_5,
            commonPayload,
            commonHeaders
          ),
        ]);
        if (!requestRef.cancelled) {
          g1 = resG1.data;
          g2 = resG2.data;
          g3 = resG3.data;
          g4 = resG4.data;
          g5 = resG5.data;
        }
      } else if (platform === "pinterest") {
        console.log("Pinterest API Debug:", {
          BrandId,
          subscriptionId,
          token: token ? "Token exists" : "No token",
          dateRange,
        });
        const [resG1, resG2] = await Promise.all([
          apiInstance.get(URL.ANALYTICS_PINTEREST_GARPH_1, commonHeaders),
          apiInstance.post(
            URL.ANALYTICS_PINTEREST_GARPH_2,
            commonPayload,
            commonHeaders
          ),
        ]);
        if (!requestRef.cancelled) {
          g1 = resG1.data;
          g2 = resG2.data;
        }
      }

      // Only update if request wasn't cancelled
      if (!requestRef.cancelled && onPlatformDataChange) {
        const platformData = {
          graph1: g1,
          graph2: g2,
          graph3: g3,
          graph4: g4,
          graph5: g5,
        };

        console.log("Fetched platformData:", platformData); // Debug log
        onPlatformDataChange(platformData);
      }
    } catch (error) {
      console.error("Error fetching platform data:", error);
      if (!requestRef.cancelled && onPlatformDataChange) {
        onPlatformDataChange({});
      }
    }
  };

  // Handle platform click
  const handlePlatformClick = (platform) => {
    setSelectedPlatform(platform.id);
    if (setActiveChannel) {
      setActiveChannel(platform.id);
    }
    if (onPlatformChange) {
      onPlatformChange(platform.id, platform.name, platform.active);
    }
    if (onPlatformSelect) {
      onPlatformSelect(platform);
    }

    // Clear previous data and fetch new data
    if (onPlatformDataChange) {
      onPlatformDataChange(null);
    }
  };

  // Effect to load channels when brand changes
  useEffect(() => {
    if (selectedBrand) {
      getChannels();
      // Reset selected platform when brand changes
      setSelectedPlatform(null);
      if (setActiveChannel) {
        setActiveChannel(null);
      }
      isInitializedRef.current = false;
    }
  }, [selectedBrand?.id]); // Only depend on brand ID

  // Effect to set default platform when platforms are loaded
  useEffect(() => {
    if (
      platforms.length > 0 &&
      !selectedPlatform &&
      !isInitializedRef.current
    ) {
      const activePlatforms = platforms.filter((p) => p.platformStatus === "1");
      if (activePlatforms.length > 0) {
        const defaultPlatform =
          activePlatforms.find((p) => p.active) || activePlatforms[0];
        setSelectedPlatform(defaultPlatform.id);
        if (setActiveChannel) {
          setActiveChannel(defaultPlatform.id);
        }
        if (onPlatformSelect) {
          onPlatformSelect(defaultPlatform);
        }
        if (onPlatformChange) {
          onPlatformChange(
            defaultPlatform.id,
            defaultPlatform.name,
            defaultPlatform.active
          );
        }
        isInitializedRef.current = true;
      }
    }
  }, [platforms.length]); // Only depend on platforms length

  // Effect to handle external activeChannel changes
  useEffect(() => {
    if (
      activeChannel &&
      activeChannel !== selectedPlatform &&
      platforms.length > 0
    ) {
      const platform = platforms.find((p) => p.id === activeChannel);
      if (platform) {
        setSelectedPlatform(activeChannel);
      }
    }
  }, [activeChannel, platforms.length]); // Removed selectedPlatform from deps

  // Effect to fetch platform data when selectedPlatform or dateRange changes
  useEffect(() => {
    if (selectedPlatform && platforms.length > 0 && dateRange) {
      fetchPlatformData(selectedPlatform);
    }
  }, [
    selectedPlatform,
    dateRange?.startDate,
    dateRange?.endDate,
    platforms.length,
  ]);

  return (
    <div className="bg-[#FFFFFF] rounded-lg p-5 w-full md:w-[320px] h-full shadow-sm font-Ubuntu overflow-y-auto">
      {isLoading ? (
        <Loader />
      ) : (
        <div className="flex flex-col space-y-3 pt-[25px]">
          {platforms.filter((platform) => platform.platformStatus === "1")
            .length > 0 ? (
            platforms
              .filter((platform) => platform.platformStatus === "1")
              .map((platform) => (
                <div
                  key={platform.id}
                  className={`flex items-center justify-between p-3 rounded-xl border border-[#E0E0E0] cursor-pointer transition-all ${
                    selectedPlatform === platform.id
                      ? "bg-[#563D39]"
                      : "bg-white"
                  }`}
                  onClick={() => handlePlatformClick(platform)}
                >
                  <div className="flex items-center">
                    <div
                      className="w-8 h-8 mr-2 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: platform.color }}
                    >
                      {platform.icon ? (
                        <img
                          src={platform.icon}
                          alt={platform.name}
                          className="w-full h-full object-contain"
                        />
                      ) : (
                        <span className="text-white text-xs font-bold">
                          {platform.name.charAt(0)}
                        </span>
                      )}
                    </div>
                    <span
                      className={`font-normal ${
                        selectedPlatform === platform.id
                          ? "text-white"
                          : "text-[#29323A]"
                      }`}
                    >
                      {platform.name}
                    </span>
                  </div>

                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center overflow-hidden">
                    {platform.active ? (
                      <img
                        src={platform.profile || dummyProfile}
                        alt={platform.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <img
                        src={addBrand}
                        alt="+"
                        className="w-full h-full object-contain"
                      />
                    )}
                  </div>
                </div>
              ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              No platforms connected
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Sidebar;
